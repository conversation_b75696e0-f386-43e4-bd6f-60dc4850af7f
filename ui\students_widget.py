# -*- coding: utf-8 -*-
"""
واجهة إدارة الطلاب
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QComboBox, QDateEdit, QTextEdit, QDialog, QFormLayout,
                             QDialogButtonBox, QMessageBox, QHeaderView, QFrame,
                             QGroupBox, QGridLayout, QSplitter)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont

from models import Student
from datetime import date

class StudentDialog(QDialog):
    """نافذة إضافة/تعديل طالب"""
    
    def __init__(self, student=None, parent=None):
        super().__init__(parent)
        self.student = student
        self.setWindowTitle("إضافة طالب جديد" if student is None else "تعديل بيانات الطالب")
        self.setModal(True)
        self.setFixedSize(500, 400)
        
        self.setup_ui()
        
        if self.student:
            self.load_student_data()
    
    def setup_ui(self):
        """إنشاء واجهة النافذة"""
        layout = QVBoxLayout(self)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # الاسم
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم الطالب")
        form_layout.addRow("الاسم:", self.name_edit)
        
        # رقم الهاتف
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("رقم هاتف الطالب")
        form_layout.addRow("رقم الهاتف:", self.phone_edit)
        
        # رقم ولي الأمر
        self.parent_phone_edit = QLineEdit()
        self.parent_phone_edit.setPlaceholderText("رقم هاتف ولي الأمر")
        form_layout.addRow("رقم ولي الأمر:", self.parent_phone_edit)
        
        # العنوان
        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(60)
        self.address_edit.setPlaceholderText("عنوان الطالب")
        form_layout.addRow("العنوان:", self.address_edit)
        
        # الجنس
        self.gender_combo = QComboBox()
        self.gender_combo.addItems(["ذكر", "أنثى"])
        form_layout.addRow("الجنس:", self.gender_combo)
        
        # تاريخ الميلاد
        self.birth_date_edit = QDateEdit()
        self.birth_date_edit.setDate(QDate.currentDate().addYears(-18))
        self.birth_date_edit.setCalendarPopup(True)
        form_layout.addRow("تاريخ الميلاد:", self.birth_date_edit)
        
        # البريد الإلكتروني
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("البريد الإلكتروني (اختياري)")
        form_layout.addRow("البريد الإلكتروني:", self.email_edit)
        
        layout.addLayout(form_layout)
        
        # أزرار الحفظ والإلغاء
        button_box = QDialogButtonBox(QDialogButtonBox.Save | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.save_student)
        button_box.rejected.connect(self.reject)
        
        # تعريب النصوص
        button_box.button(QDialogButtonBox.Save).setText("حفظ")
        button_box.button(QDialogButtonBox.Cancel).setText("إلغاء")
        
        layout.addWidget(button_box)
    
    def load_student_data(self):
        """تحميل بيانات الطالب للتعديل"""
        if self.student:
            self.name_edit.setText(self.student.name)
            self.phone_edit.setText(self.student.phone)
            self.parent_phone_edit.setText(self.student.parent_phone)
            self.address_edit.setPlainText(self.student.address)
            
            gender_index = 0 if self.student.gender == "ذكر" else 1
            self.gender_combo.setCurrentIndex(gender_index)
            
            if self.student.birth_date:
                qdate = QDate(self.student.birth_date.year, 
                             self.student.birth_date.month, 
                             self.student.birth_date.day)
                self.birth_date_edit.setDate(qdate)
            
            self.email_edit.setText(self.student.email)
    
    def save_student(self):
        """حفظ بيانات الطالب"""
        # التحقق من صحة البيانات
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يجب إدخال اسم الطالب!")
            return
        
        try:
            # إنشاء أو تحديث كائن الطالب
            if self.student is None:
                self.student = Student()
            
            self.student.name = self.name_edit.text().strip()
            self.student.phone = self.phone_edit.text().strip()
            self.student.parent_phone = self.parent_phone_edit.text().strip()
            self.student.address = self.address_edit.toPlainText().strip()
            self.student.gender = self.gender_combo.currentText()
            
            # تحويل تاريخ الميلاد
            qdate = self.birth_date_edit.date()
            self.student.birth_date = date(qdate.year(), qdate.month(), qdate.day())
            
            self.student.email = self.email_edit.text().strip()
            
            # حفظ في قاعدة البيانات
            self.student.save()
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحفظ: {str(e)}")

class StudentsWidget(QWidget):
    """واجهة إدارة الطلاب"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_students()
    
    def setup_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # شريط البحث والأزرار
        top_layout = QHBoxLayout()
        
        # البحث
        search_label = QLabel("البحث:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالاسم أو رقم الهاتف...")
        self.search_edit.textChanged.connect(self.search_students)
        
        # أزرار العمليات
        self.add_btn = QPushButton("إضافة طالب")
        self.edit_btn = QPushButton("تعديل")
        self.delete_btn = QPushButton("حذف")
        self.refresh_btn = QPushButton("تحديث")
        
        # ربط الأزرار بالوظائف
        self.add_btn.clicked.connect(self.add_student)
        self.edit_btn.clicked.connect(self.edit_student)
        self.delete_btn.clicked.connect(self.delete_student)
        self.refresh_btn.clicked.connect(self.load_students)
        
        # تعطيل أزرار التعديل والحذف في البداية
        self.edit_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)
        
        top_layout.addWidget(search_label)
        top_layout.addWidget(self.search_edit)
        top_layout.addStretch()
        top_layout.addWidget(self.add_btn)
        top_layout.addWidget(self.edit_btn)
        top_layout.addWidget(self.delete_btn)
        top_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(top_layout)
        
        # جدول الطلاب
        self.students_table = QTableWidget()
        self.students_table.setColumnCount(8)
        self.students_table.setHorizontalHeaderLabels([
            "الرقم", "الاسم", "الهاتف", "رقم ولي الأمر", 
            "العنوان", "الجنس", "تاريخ الميلاد", "البريد الإلكتروني"
        ])
        
        # تخصيص الجدول
        header = self.students_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        # تحديد عرض الأعمدة
        self.students_table.setColumnWidth(0, 60)   # الرقم
        self.students_table.setColumnWidth(1, 150)  # الاسم
        self.students_table.setColumnWidth(2, 120)  # الهاتف
        self.students_table.setColumnWidth(3, 120)  # رقم ولي الأمر
        self.students_table.setColumnWidth(4, 200)  # العنوان
        self.students_table.setColumnWidth(5, 80)   # الجنس
        self.students_table.setColumnWidth(6, 100)  # تاريخ الميلاد
        
        # ربط حدث تحديد الصف
        self.students_table.selectionModel().selectionChanged.connect(self.on_selection_changed)
        self.students_table.doubleClicked.connect(self.edit_student)
        
        layout.addWidget(self.students_table)
        
        # تطبيق الستايل
        self.apply_style()
    
    def apply_style(self):
        """تطبيق الستايل على الواجهة"""
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #4CAF50;
                selection-color: white;
            }
            
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e0e0e0;
            }
            
            QHeaderView::section {
                background-color: #2E7D32;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
            
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 4px;
                font-size: 12px;
            }
            
            QLineEdit:focus {
                border-color: #4CAF50;
            }
        """)
    
    def load_students(self):
        """تحميل قائمة الطلاب"""
        try:
            students = Student.get_all()
            self.populate_table(students)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")
    
    def populate_table(self, students):
        """ملء الجدول بالطلاب"""
        self.students_table.setRowCount(len(students))
        
        for row, student in enumerate(students):
            self.students_table.setItem(row, 0, QTableWidgetItem(str(student.id)))
            self.students_table.setItem(row, 1, QTableWidgetItem(student.name))
            self.students_table.setItem(row, 2, QTableWidgetItem(student.phone))
            self.students_table.setItem(row, 3, QTableWidgetItem(student.parent_phone))
            self.students_table.setItem(row, 4, QTableWidgetItem(student.address))
            self.students_table.setItem(row, 5, QTableWidgetItem(student.gender))
            
            birth_date_str = student.birth_date.strftime("%Y-%m-%d") if student.birth_date else ""
            self.students_table.setItem(row, 6, QTableWidgetItem(birth_date_str))
            
            self.students_table.setItem(row, 7, QTableWidgetItem(student.email))
            
            # حفظ معرف الطالب في البيانات المخفية
            self.students_table.item(row, 0).setData(Qt.UserRole, student.id)
    
    def search_students(self):
        """البحث عن الطلاب"""
        keyword = self.search_edit.text().strip()
        if keyword:
            try:
                students = Student.search(keyword)
                self.populate_table(students)
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث: {str(e)}")
        else:
            self.load_students()
    
    def on_selection_changed(self):
        """التعامل مع تغيير التحديد في الجدول"""
        has_selection = len(self.students_table.selectionModel().selectedRows()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
    
    def add_student(self):
        """إضافة طالب جديد"""
        dialog = StudentDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_students()
            QMessageBox.information(self, "نجح", "تم إضافة الطالب بنجاح!")
    
    def edit_student(self):
        """تعديل طالب محدد"""
        current_row = self.students_table.currentRow()
        if current_row >= 0:
            student_id = self.students_table.item(current_row, 0).data(Qt.UserRole)
            student = Student.get_by_id(student_id)
            
            if student:
                dialog = StudentDialog(student, parent=self)
                if dialog.exec_() == QDialog.Accepted:
                    self.load_students()
                    QMessageBox.information(self, "نجح", "تم تحديث بيانات الطالب بنجاح!")
    
    def delete_student(self):
        """حذف طالب محدد"""
        current_row = self.students_table.currentRow()
        if current_row >= 0:
            student_name = self.students_table.item(current_row, 1).text()
            
            reply = QMessageBox.question(self, "تأكيد الحذف", 
                                       f"هل تريد حذف الطالب '{student_name}'؟\n"
                                       "هذا الإجراء لا يمكن التراجع عنه!",
                                       QMessageBox.Yes | QMessageBox.No,
                                       QMessageBox.No)
            
            if reply == QMessageBox.Yes:
                try:
                    student_id = self.students_table.item(current_row, 0).data(Qt.UserRole)
                    student = Student.get_by_id(student_id)
                    
                    if student and student.delete():
                        self.load_students()
                        QMessageBox.information(self, "نجح", "تم حذف الطالب بنجاح!")
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في حذف الطالب!")
                        
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")
