# -*- coding: utf-8 -*-
"""
النافذة الرئيسية لنظام إدارة الأكاديمية
"""

import sys
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QTabWidget, QMenuBar, QStatusBar, QAction, 
                             QMessageBox, QApplication, QLabel, QPushButton,
                             QFrame, QGridLayout, QSizePolicy)
from PyQt5.QtCore import Qt, QTimer, QDateTime
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor

from ui.students_widget import StudentsWidget
from ui.teachers_widget import TeachersWidget
from ui.courses_widget import CoursesWidget
from ui.groups_widget import GroupsWidget
from ui.attendance_widget import AttendanceWidget
from ui.payments_widget import PaymentsWidget
from ui.reports_widget import ReportsWidget
from ui.settings_widget import SettingsWidget
from database import db_manager

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نظام إدارة الأكاديمية")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(1000, 700)
        
        # تطبيق الستايل
        self.apply_style()
        
        # إنشاء واجهة المستخدم
        self.setup_ui()
        
        # إنشاء شريط القوائم
        self.create_menu_bar()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
        # تحديث الوقت كل ثانية
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)
        
        self.update_time()
    
    def apply_style(self):
        """تطبيق الستايل على النافذة"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
                border-radius: 5px;
            }
            
            QTabWidget::tab-bar {
                alignment: center;
            }
            
            QTabBar::tab {
                background-color: #e1e1e1;
                border: 1px solid #c0c0c0;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
                font-size: 12px;
                font-weight: bold;
            }
            
            QTabBar::tab:selected {
                background-color: #4CAF50;
                color: white;
            }
            
            QTabBar::tab:hover {
                background-color: #66BB6A;
                color: white;
            }
            
            QMenuBar {
                background-color: #2E7D32;
                color: white;
                font-size: 12px;
                font-weight: bold;
                padding: 5px;
            }
            
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
                border-radius: 3px;
            }
            
            QMenuBar::item:selected {
                background-color: #388E3C;
            }
            
            QStatusBar {
                background-color: #2E7D32;
                color: white;
                font-size: 11px;
                border-top: 1px solid #1B5E20;
            }
            
            QLabel {
                font-size: 12px;
            }
            
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #45a049;
            }
            
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
    
    def setup_ui(self):
        """إنشاء واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        
        # إضافة التبويبات
        self.students_widget = StudentsWidget()
        self.teachers_widget = TeachersWidget()
        self.courses_widget = CoursesWidget()
        self.groups_widget = GroupsWidget()
        self.attendance_widget = AttendanceWidget()
        self.payments_widget = PaymentsWidget()
        self.reports_widget = ReportsWidget()
        self.settings_widget = SettingsWidget()
        
        self.tab_widget.addTab(self.students_widget, "👥 الطلاب")
        self.tab_widget.addTab(self.teachers_widget, "👨‍🏫 المعلمين")
        self.tab_widget.addTab(self.courses_widget, "📚 الكورسات")
        self.tab_widget.addTab(self.groups_widget, "👥 المجموعات")
        self.tab_widget.addTab(self.attendance_widget, "📋 الحضور والغياب")
        self.tab_widget.addTab(self.payments_widget, "💰 الدفعات")
        self.tab_widget.addTab(self.reports_widget, "📊 التقارير")
        self.tab_widget.addTab(self.settings_widget, "⚙️ الإعدادات")
        
        main_layout.addWidget(self.tab_widget)
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu('ملف')
        
        # نسخة احتياطية
        backup_action = QAction('إنشاء نسخة احتياطية', self)
        backup_action.triggered.connect(self.create_backup)
        file_menu.addAction(backup_action)
        
        file_menu.addSeparator()
        
        # خروج
        exit_action = QAction('خروج', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu('مساعدة')
        
        about_action = QAction('حول البرنامج', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = self.statusBar()
        
        # تسمية الوقت
        self.time_label = QLabel()
        self.status_bar.addPermanentWidget(self.time_label)
        
        # رسالة الحالة
        self.status_bar.showMessage("مرحباً بك في نظام إدارة الأكاديمية")
    
    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        current_time = QDateTime.currentDateTime()
        time_text = current_time.toString("yyyy-MM-dd hh:mm:ss")
        self.time_label.setText(f"الوقت: {time_text}")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            # جلب مسار النسخ الاحتياطي من الإعدادات
            backup_path = "./backups"  # مسار افتراضي
            
            if db_manager.backup_database(backup_path):
                QMessageBox.information(self, "نجح", "تم إنشاء النسخة الاحتياطية بنجاح!")
                self.status_bar.showMessage("تم إنشاء النسخة الاحتياطية بنجاح", 3000)
            else:
                QMessageBox.warning(self, "خطأ", "فشل في إنشاء النسخة الاحتياطية!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")
    
    def show_about(self):
        """عرض معلومات حول البرنامج"""
        QMessageBox.about(self, "حول البرنامج", 
                         "نظام إدارة الأكاديمية\n"
                         "الإصدار 1.0\n\n"
                         "برنامج شامل لإدارة الأكاديميات التعليمية\n"
                         "يتضمن إدارة الطلاب والمعلمين والكورسات والمجموعات\n"
                         "والحضور والغياب والدفعات والتقارير\n\n"
                         "تم التطوير باستخدام Python و PyQt5")
    
    def closeEvent(self, event):
        """التعامل مع إغلاق النافذة"""
        reply = QMessageBox.question(self, 'تأكيد الخروج', 
                                   'هل تريد إغلاق البرنامج؟',
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()

def main():
    """تشغيل التطبيق"""
    app = QApplication(sys.argv)
    
    # تعيين خط عربي
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # تعيين اتجاه النص من اليمين لليسار
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة الرئيسية
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
