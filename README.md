# نظام إدارة الأكاديمية

برنامج شامل لإدارة الأكاديميات التعليمية مطور باستخدام Python و PyQt5

## المميزات

### ✅ المميزات المكتملة:
- **إدارة الطلاب**: إضافة، تعديل، حذف، والبحث عن الطلاب
- **إدارة المعلمين**: إدارة بيانات المعلمين ومرتباتهم
- **إدارة الكورسات**: إنشاء وإدارة الكورسات (حضوري/أونلاين)
- **إدارة المجموعات**: تنظيم الطلاب في مجموعات مع المعلمين والكورسات
- **قاعدة بيانات SQLite**: حفظ البيانات محلياً
- **واجهة مستخدم عربية**: تصميم جذاب وسهل الاستخدام
- **النسخ الاحتياطي**: إمكانية إنشاء نسخ احتياطية من قاعدة البيانات

### 🔄 المميزات قيد التطوير:
- نظام الحضور والغياب
- إدارة الدفعات والمديونيات
- طباعة الإيصالات بصيغة PDF
- التقارير والإحصائيات
- تصدير البيانات (Excel/PDF)
- نظام المستخدمين والصلاحيات

## متطلبات النظام

- Python 3.7 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd academy-management-system
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل البرنامج
```bash
python main.py
```

## هيكل المشروع

```
academy-management-system/
├── main.py                 # الملف الرئيسي للتطبيق
├── database.py            # إدارة قاعدة البيانات
├── models.py              # نماذج البيانات
├── requirements.txt       # متطلبات المشروع
├── ui/                    # واجهات المستخدم
│   ├── main_window.py     # النافذة الرئيسية
│   ├── students_widget.py # واجهة إدارة الطلاب
│   ├── teachers_widget.py # واجهة إدارة المعلمين
│   ├── courses_widget.py  # واجهة إدارة الكورسات
│   ├── groups_widget.py   # واجهة إدارة المجموعات
│   ├── attendance_widget.py # واجهة الحضور والغياب
│   ├── payments_widget.py # واجهة الدفعات
│   ├── reports_widget.py  # واجهة التقارير
│   └── settings_widget.py # واجهة الإعدادات
├── utils/                 # أدوات مساعدة (قريباً)
├── resources/             # الموارد والأيقونات (قريباً)
└── backups/              # مجلد النسخ الاحتياطي
```

## قاعدة البيانات

يستخدم البرنامج قاعدة بيانات SQLite مع الجداول التالية:

- **students**: بيانات الطلاب
- **teachers**: بيانات المعلمين
- **courses**: بيانات الكورسات
- **groups**: بيانات المجموعات
- **group_students**: ربط الطلاب بالمجموعات
- **attendance**: سجل الحضور والغياب
- **payments**: سجل الدفعات
- **users**: المستخدمين والصلاحيات
- **settings**: إعدادات النظام

## الاستخدام

### إدارة الطلاب
1. انتقل إلى تبويب "الطلاب"
2. اضغط "إضافة طالب" لإضافة طالب جديد
3. املأ البيانات المطلوبة واضغط "حفظ"
4. يمكنك البحث والتعديل والحذف من خلال الجدول

### إدارة المعلمين
1. انتقل إلى تبويب "المعلمين"
2. اضغط "إضافة معلم" لإضافة معلم جديد
3. أدخل بيانات المعلم بما في ذلك المرتب والتخصص

### إدارة الكورسات
1. انتقل إلى تبويب "الكورسات"
2. اضغط "إضافة كورس" لإنشاء كورس جديد
3. حدد نوع الكورس (حضوري/أونلاين)
4. أدخل السعر وعدد الحصص

### إدارة المجموعات
1. انتقل إلى تبويب "المجموعات"
2. اضغط "إضافة مجموعة"
3. اختر الكورس والمعلم
4. حدد تواريخ البداية والنهاية والجدول الزمني

## النسخ الاحتياطي

- يمكن إنشاء نسخة احتياطية من قائمة "ملف" > "إنشاء نسخة احتياطية"
- يتم حفظ النسخ الاحتياطية في مجلد `backups/`
- اسم الملف يتضمن التاريخ والوقت

## تجميع البرنامج كملف EXE

```bash
pip install pyinstaller
pyinstaller --onefile --windowed --name="Academy-Management" main.py
```

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في المستودع.

## خارطة الطريق

### الإصدار 1.1 (قريباً)
- [ ] إكمال نظام الحضور والغياب
- [ ] إدارة الدفعات والمديونيات
- [ ] طباعة الإيصالات

### الإصدار 1.2
- [ ] التقارير والإحصائيات
- [ ] تصدير البيانات
- [ ] نظام المستخدمين والصلاحيات

### الإصدار 2.0
- [ ] واجهة ويب
- [ ] تطبيق موبايل
- [ ] التزامن السحابي

---

**تم التطوير بـ ❤️ باستخدام Python و PyQt5**
