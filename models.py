# -*- coding: utf-8 -*-
"""
نماذج البيانات لنظام إدارة الأكاديمية
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from database import db_manager

class Student:
    """نموذج الطالب"""
    
    def __init__(self, id: Optional[int] = None, name: str = "", phone: str = "", 
                 parent_phone: str = "", address: str = "", gender: str = "ذكر",
                 birth_date: Optional[date] = None, email: str = ""):
        self.id = id
        self.name = name
        self.phone = phone
        self.parent_phone = parent_phone
        self.address = address
        self.gender = gender
        self.birth_date = birth_date
        self.email = email
    
    def save(self) -> int:
        """حفظ الطالب في قاعدة البيانات"""
        if self.id:
            # تحديث طالب موجود
            query = '''
                UPDATE students 
                SET name=?, phone=?, parent_phone=?, address=?, gender=?, 
                    birth_date=?, email=?, updated_at=CURRENT_TIMESTAMP
                WHERE id=?
            '''
            params = (self.name, self.phone, self.parent_phone, self.address, 
                     self.gender, self.birth_date, self.email, self.id)
            db_manager.execute_update(query, params)
            return self.id
        else:
            # إضافة طالب جديد
            query = '''
                INSERT INTO students (name, phone, parent_phone, address, gender, birth_date, email)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            '''
            params = (self.name, self.phone, self.parent_phone, self.address, 
                     self.gender, self.birth_date, self.email)
            self.id = db_manager.get_last_insert_id(query, params)
            return self.id
    
    def delete(self) -> bool:
        """حذف الطالب"""
        if self.id:
            query = "DELETE FROM students WHERE id=?"
            return db_manager.execute_update(query, (self.id,)) > 0
        return False
    
    @staticmethod
    def get_all() -> List['Student']:
        """جلب جميع الطلاب"""
        query = "SELECT * FROM students ORDER BY name"
        rows = db_manager.execute_query(query)
        return [Student.from_row(row) for row in rows]
    
    @staticmethod
    def get_by_id(student_id: int) -> Optional['Student']:
        """جلب طالب بالمعرف"""
        query = "SELECT * FROM students WHERE id=?"
        rows = db_manager.execute_query(query, (student_id,))
        return Student.from_row(rows[0]) if rows else None
    
    @staticmethod
    def search(keyword: str) -> List['Student']:
        """البحث عن الطلاب"""
        query = '''
            SELECT * FROM students 
            WHERE name LIKE ? OR phone LIKE ? OR parent_phone LIKE ?
            ORDER BY name
        '''
        keyword = f"%{keyword}%"
        rows = db_manager.execute_query(query, (keyword, keyword, keyword))
        return [Student.from_row(row) for row in rows]
    
    @staticmethod
    def from_row(row) -> 'Student':
        """إنشاء كائن طالب من صف قاعدة البيانات"""
        birth_date = None
        if row['birth_date']:
            birth_date = datetime.strptime(row['birth_date'], '%Y-%m-%d').date()
        
        return Student(
            id=row['id'],
            name=row['name'],
            phone=row['phone'] or "",
            parent_phone=row['parent_phone'] or "",
            address=row['address'] or "",
            gender=row['gender'] or "ذكر",
            birth_date=birth_date,
            email=row['email'] or ""
        )

class Teacher:
    """نموذج المعلم"""
    
    def __init__(self, id: Optional[int] = None, name: str = "", phone: str = "",
                 salary: float = 0.0, specialization: str = ""):
        self.id = id
        self.name = name
        self.phone = phone
        self.salary = salary
        self.specialization = specialization
    
    def save(self) -> int:
        """حفظ المعلم في قاعدة البيانات"""
        if self.id:
            query = '''
                UPDATE teachers 
                SET name=?, phone=?, salary=?, specialization=?, updated_at=CURRENT_TIMESTAMP
                WHERE id=?
            '''
            params = (self.name, self.phone, self.salary, self.specialization, self.id)
            db_manager.execute_update(query, params)
            return self.id
        else:
            query = '''
                INSERT INTO teachers (name, phone, salary, specialization)
                VALUES (?, ?, ?, ?)
            '''
            params = (self.name, self.phone, self.salary, self.specialization)
            self.id = db_manager.get_last_insert_id(query, params)
            return self.id
    
    def delete(self) -> bool:
        """حذف المعلم"""
        if self.id:
            query = "DELETE FROM teachers WHERE id=?"
            return db_manager.execute_update(query, (self.id,)) > 0
        return False
    
    @staticmethod
    def get_all() -> List['Teacher']:
        """جلب جميع المعلمين"""
        query = "SELECT * FROM teachers ORDER BY name"
        rows = db_manager.execute_query(query)
        return [Teacher.from_row(row) for row in rows]
    
    @staticmethod
    def get_by_id(teacher_id: int) -> Optional['Teacher']:
        """جلب معلم بالمعرف"""
        query = "SELECT * FROM teachers WHERE id=?"
        rows = db_manager.execute_query(query, (teacher_id,))
        return Teacher.from_row(rows[0]) if rows else None
    
    @staticmethod
    def from_row(row) -> 'Teacher':
        """إنشاء كائن معلم من صف قاعدة البيانات"""
        return Teacher(
            id=row['id'],
            name=row['name'],
            phone=row['phone'] or "",
            salary=row['salary'] or 0.0,
            specialization=row['specialization'] or ""
        )

class Course:
    """نموذج الكورس"""
    
    def __init__(self, id: Optional[int] = None, name: str = "", price: float = 0.0,
                 sessions_count: int = 1, type: str = "حضوري", session_link: str = "",
                 description: str = ""):
        self.id = id
        self.name = name
        self.price = price
        self.sessions_count = sessions_count
        self.type = type
        self.session_link = session_link
        self.description = description
    
    def save(self) -> int:
        """حفظ الكورس في قاعدة البيانات"""
        if self.id:
            query = '''
                UPDATE courses 
                SET name=?, price=?, sessions_count=?, type=?, session_link=?, 
                    description=?, updated_at=CURRENT_TIMESTAMP
                WHERE id=?
            '''
            params = (self.name, self.price, self.sessions_count, self.type, 
                     self.session_link, self.description, self.id)
            db_manager.execute_update(query, params)
            return self.id
        else:
            query = '''
                INSERT INTO courses (name, price, sessions_count, type, session_link, description)
                VALUES (?, ?, ?, ?, ?, ?)
            '''
            params = (self.name, self.price, self.sessions_count, self.type, 
                     self.session_link, self.description)
            self.id = db_manager.get_last_insert_id(query, params)
            return self.id
    
    def delete(self) -> bool:
        """حذف الكورس"""
        if self.id:
            query = "DELETE FROM courses WHERE id=?"
            return db_manager.execute_update(query, (self.id,)) > 0
        return False
    
    @staticmethod
    def get_all() -> List['Course']:
        """جلب جميع الكورسات"""
        query = "SELECT * FROM courses ORDER BY name"
        rows = db_manager.execute_query(query)
        return [Course.from_row(row) for row in rows]
    
    @staticmethod
    def get_by_id(course_id: int) -> Optional['Course']:
        """جلب كورس بالمعرف"""
        query = "SELECT * FROM courses WHERE id=?"
        rows = db_manager.execute_query(query, (course_id,))
        return Course.from_row(rows[0]) if rows else None
    
    @staticmethod
    def from_row(row) -> 'Course':
        """إنشاء كائن كورس من صف قاعدة البيانات"""
        return Course(
            id=row['id'],
            name=row['name'],
            price=row['price'] or 0.0,
            sessions_count=row['sessions_count'] or 1,
            type=row['type'] or "حضوري",
            session_link=row['session_link'] or "",
            description=row['description'] or ""
        )

class Group:
    """نموذج المجموعة"""
    
    def __init__(self, id: Optional[int] = None, name: str = "", course_id: int = 0,
                 teacher_id: int = 0, start_date: Optional[date] = None,
                 end_date: Optional[date] = None, schedule: str = "", max_students: int = 20):
        self.id = id
        self.name = name
        self.course_id = course_id
        self.teacher_id = teacher_id
        self.start_date = start_date
        self.end_date = end_date
        self.schedule = schedule
        self.max_students = max_students
    
    def save(self) -> int:
        """حفظ المجموعة في قاعدة البيانات"""
        if self.id:
            query = '''
                UPDATE groups 
                SET name=?, course_id=?, teacher_id=?, start_date=?, end_date=?, 
                    schedule=?, max_students=?, updated_at=CURRENT_TIMESTAMP
                WHERE id=?
            '''
            params = (self.name, self.course_id, self.teacher_id, self.start_date,
                     self.end_date, self.schedule, self.max_students, self.id)
            db_manager.execute_update(query, params)
            return self.id
        else:
            query = '''
                INSERT INTO groups (name, course_id, teacher_id, start_date, end_date, schedule, max_students)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            '''
            params = (self.name, self.course_id, self.teacher_id, self.start_date,
                     self.end_date, self.schedule, self.max_students)
            self.id = db_manager.get_last_insert_id(query, params)
            return self.id
    
    @staticmethod
    def get_all() -> List['Group']:
        """جلب جميع المجموعات مع بيانات الكورس والمعلم"""
        query = '''
            SELECT g.*, c.name as course_name, t.name as teacher_name
            FROM groups g
            LEFT JOIN courses c ON g.course_id = c.id
            LEFT JOIN teachers t ON g.teacher_id = t.id
            ORDER BY g.name
        '''
        rows = db_manager.execute_query(query)
        return [Group.from_row_with_details(row) for row in rows]
    
    @staticmethod
    def from_row_with_details(row) -> 'Group':
        """إنشاء كائن مجموعة من صف قاعدة البيانات مع التفاصيل"""
        start_date = None
        end_date = None
        if row['start_date']:
            start_date = datetime.strptime(row['start_date'], '%Y-%m-%d').date()
        if row['end_date']:
            end_date = datetime.strptime(row['end_date'], '%Y-%m-%d').date()
        
        group = Group(
            id=row['id'],
            name=row['name'],
            course_id=row['course_id'],
            teacher_id=row['teacher_id'],
            start_date=start_date,
            end_date=end_date,
            schedule=row['schedule'] or "",
            max_students=row['max_students'] or 20
        )
        # إضافة بيانات إضافية
        group.course_name = row.get('course_name', '')
        group.teacher_name = row.get('teacher_name', '')
        return group
