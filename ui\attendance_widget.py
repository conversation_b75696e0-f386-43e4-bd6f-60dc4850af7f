# -*- coding: utf-8 -*-
"""
واجهة إدارة الحضور والغياب
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QComboBox, QDateEdit, QTableWidget,
                             QTableWidgetItem, QMessageBox, QHeaderView)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

class AttendanceWidget(QWidget):
    """واجهة إدارة الحضور والغياب"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # شريط التحكم العلوي
        top_layout = QHBoxLayout()
        
        # اختيار المجموعة
        group_label = QLabel("المجموعة:")
        self.group_combo = QComboBox()
        self.group_combo.addItem("اختر المجموعة...")
        
        # اختيار التاريخ
        date_label = QLabel("التاريخ:")
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        
        # أزرار العمليات
        self.load_btn = QPushButton("تحميل الطلاب")
        self.save_btn = QPushButton("حفظ الحضور")
        
        top_layout.addWidget(group_label)
        top_layout.addWidget(self.group_combo)
        top_layout.addWidget(date_label)
        top_layout.addWidget(self.date_edit)
        top_layout.addStretch()
        top_layout.addWidget(self.load_btn)
        top_layout.addWidget(self.save_btn)
        
        layout.addLayout(top_layout)
        
        # جدول الحضور
        self.attendance_table = QTableWidget()
        self.attendance_table.setColumnCount(4)
        self.attendance_table.setHorizontalHeaderLabels([
            "الرقم", "اسم الطالب", "الحالة", "ملاحظات"
        ])
        
        # تخصيص الجدول
        header = self.attendance_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        layout.addWidget(self.attendance_table)
        
        # رسالة مؤقتة
        info_label = QLabel("ستتم إضافة وظائف الحضور والغياب قريباً...")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #666; font-size: 14px; padding: 20px;")
        layout.addWidget(info_label)
        
        # تطبيق الستايل
        self.apply_style()
    
    def apply_style(self):
        """تطبيق الستايل على الواجهة"""
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #4CAF50;
                selection-color: white;
            }
            
            QHeaderView::section {
                background-color: #2E7D32;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
            
            QComboBox, QDateEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 4px;
                font-size: 12px;
            }
        """)
