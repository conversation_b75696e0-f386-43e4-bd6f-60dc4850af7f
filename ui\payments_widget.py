# -*- coding: utf-8 -*-
"""
واجهة إدارة الدفعات
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QComboBox, QDateEdit, QTableWidget,
                             QTableWidgetItem, QMessageBox, QHeaderView,
                             QLineEdit, QDoubleSpinBox)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

class PaymentsWidget(QWidget):
    """واجهة إدارة الدفعات"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # شريط التحكم العلوي
        top_layout = QHBoxLayout()
        
        # البحث
        search_label = QLabel("البحث:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالطالب أو رقم الإيصال...")
        
        # فلتر نوع الدفع
        payment_type_label = QLabel("نوع الدفع:")
        self.payment_type_combo = QComboBox()
        self.payment_type_combo.addItems(["الكل", "نقدي", "تحويل", "شيك"])
        
        # أزرار العمليات
        self.add_payment_btn = QPushButton("إضافة دفعة")
        self.print_receipt_btn = QPushButton("طباعة إيصال")
        self.refresh_btn = QPushButton("تحديث")
        
        top_layout.addWidget(search_label)
        top_layout.addWidget(self.search_edit)
        top_layout.addWidget(payment_type_label)
        top_layout.addWidget(self.payment_type_combo)
        top_layout.addStretch()
        top_layout.addWidget(self.add_payment_btn)
        top_layout.addWidget(self.print_receipt_btn)
        top_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(top_layout)
        
        # جدول الدفعات
        self.payments_table = QTableWidget()
        self.payments_table.setColumnCount(7)
        self.payments_table.setHorizontalHeaderLabels([
            "الرقم", "الطالب", "المجموعة", "المبلغ", "تاريخ الدفع", "نوع الدفع", "رقم الإيصال"
        ])
        
        # تخصيص الجدول
        header = self.payments_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        # تحديد عرض الأعمدة
        self.payments_table.setColumnWidth(0, 60)   # الرقم
        self.payments_table.setColumnWidth(1, 150)  # الطالب
        self.payments_table.setColumnWidth(2, 150)  # المجموعة
        self.payments_table.setColumnWidth(3, 100)  # المبلغ
        self.payments_table.setColumnWidth(4, 100)  # تاريخ الدفع
        self.payments_table.setColumnWidth(5, 80)   # نوع الدفع
        
        layout.addWidget(self.payments_table)
        
        # إحصائيات سريعة
        stats_layout = QHBoxLayout()
        
        self.total_payments_label = QLabel("إجمالي الدفعات: 0 ريال")
        self.total_payments_label.setStyleSheet("font-weight: bold; color: #2E7D32;")
        
        self.monthly_payments_label = QLabel("دفعات هذا الشهر: 0 ريال")
        self.monthly_payments_label.setStyleSheet("font-weight: bold; color: #1976D2;")
        
        stats_layout.addWidget(self.total_payments_label)
        stats_layout.addStretch()
        stats_layout.addWidget(self.monthly_payments_label)
        
        layout.addLayout(stats_layout)
        
        # رسالة مؤقتة
        info_label = QLabel("ستتم إضافة وظائف إدارة الدفعات قريباً...")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #666; font-size: 14px; padding: 20px;")
        layout.addWidget(info_label)
        
        # تطبيق الستايل
        self.apply_style()
    
    def apply_style(self):
        """تطبيق الستايل على الواجهة"""
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #4CAF50;
                selection-color: white;
            }
            
            QHeaderView::section {
                background-color: #2E7D32;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
            
            QLineEdit, QComboBox {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 4px;
                font-size: 12px;
            }
            
            QLineEdit:focus, QComboBox:focus {
                border-color: #4CAF50;
            }
        """)
