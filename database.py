# -*- coding: utf-8 -*-
"""
إدارة قاعدة البيانات SQLite لنظام إدارة الأكاديمية
"""

import sqlite3
import os
from datetime import datetime
from typing import Optional, List, Dict, Any

class DatabaseManager:
    def __init__(self, db_path: str = "academy.db"):
        """
        تهيئة مدير قاعدة البيانات

        Args:
            db_path: مسار ملف قاعدة البيانات
        """
        self.db_path = db_path
        self.init_database()

    def get_connection(self) -> sqlite3.Connection:
        """إنشاء اتصال جديد بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
        return conn

    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # جدول الطلاب
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS students (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_id TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    phone TEXT,
                    parent_phone TEXT,
                    address TEXT,
                    gender TEXT CHECK(gender IN ('ذكر', 'أنثى')),
                    birth_date DATE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # جدول المعلمين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS teachers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    phone TEXT,
                    salary REAL DEFAULT 0,
                    specialization TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # جدول الكورسات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS courses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    price REAL NOT NULL DEFAULT 0,
                    sessions_count INTEGER DEFAULT 1,
                    type TEXT CHECK(type IN ('حضوري', 'أونلاين')) DEFAULT 'حضوري',
                    session_link TEXT,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # جدول المجموعات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS groups (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    course_id INTEGER NOT NULL,
                    teacher_id INTEGER NOT NULL,
                    start_date DATE,
                    end_date DATE,
                    schedule TEXT,
                    max_students INTEGER DEFAULT 20,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (course_id) REFERENCES courses (id),
                    FOREIGN KEY (teacher_id) REFERENCES teachers (id)
                )
            ''')

            # جدول تسجيل الطلاب في المجموعات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS group_students (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    group_id INTEGER NOT NULL,
                    student_id INTEGER NOT NULL,
                    enrollment_date DATE DEFAULT CURRENT_DATE,
                    status TEXT DEFAULT 'نشط' CHECK(status IN ('نشط', 'متوقف', 'مكتمل')),
                    FOREIGN KEY (group_id) REFERENCES groups (id),
                    FOREIGN KEY (student_id) REFERENCES students (id),
                    UNIQUE(group_id, student_id)
                )
            ''')

            # جدول الحضور والغياب
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS attendance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_id INTEGER NOT NULL,
                    group_id INTEGER NOT NULL,
                    date DATE NOT NULL,
                    status TEXT CHECK(status IN ('حاضر', 'غائب', 'متأخر')) DEFAULT 'حاضر',
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (student_id) REFERENCES students (id),
                    FOREIGN KEY (group_id) REFERENCES groups (id),
                    UNIQUE(student_id, group_id, date)
                )
            ''')

            # جدول الدفعات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS payments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_id INTEGER NOT NULL,
                    group_id INTEGER,
                    amount REAL NOT NULL,
                    payment_date DATE DEFAULT CURRENT_DATE,
                    payment_type TEXT DEFAULT 'نقدي' CHECK(payment_type IN ('نقدي', 'تحويل', 'شيك')),
                    description TEXT,
                    receipt_number TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (student_id) REFERENCES students (id),
                    FOREIGN KEY (group_id) REFERENCES groups (id)
                )
            ''')

            # جدول المستخدمين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password TEXT NOT NULL,
                    role TEXT CHECK(role IN ('مدير', 'موظف')) DEFAULT 'موظف',
                    full_name TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP
                )
            ''')

            # جدول الإعدادات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT UNIQUE NOT NULL,
                    value TEXT,
                    description TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # إدراج الإعدادات الافتراضية
            default_settings = [
                ('academy_name', 'أكاديمية التعلم', 'اسم الأكاديمية'),
                ('session_duration', '60', 'مدة الحصة بالدقائق'),
                ('default_online_link', 'https://zoom.us/j/meeting', 'رابط الجلسة الافتراضي'),
                ('currency', 'ريال', 'العملة المستخدمة'),
                ('backup_path', './backups', 'مسار النسخ الاحتياطي'),
            ]

            cursor.executemany('''
                INSERT OR IGNORE INTO settings (key, value, description)
                VALUES (?, ?, ?)
            ''', default_settings)

            # إنشاء مستخدم افتراضي (admin/admin)
            cursor.execute('''
                INSERT OR IGNORE INTO users (username, password, role, full_name)
                VALUES ('admin', 'admin', 'مدير', 'المدير العام')
            ''')

            # إضافة عمود student_id إذا لم يكن موجوداً (للتوافق مع قواعد البيانات القديمة)
            try:
                cursor.execute("ALTER TABLE students ADD COLUMN student_id TEXT")
                print("تم إضافة عمود الرقم التعريفي للطلاب")
            except sqlite3.OperationalError:
                # العمود موجود بالفعل
                pass

            # إنشاء أرقام تعريفية للطلاب الموجودين بدون رقم تعريفي
            cursor.execute('''
                UPDATE students
                SET student_id = CASE
                    WHEN student_id IS NULL OR student_id = ''
                    THEN strftime('%Y', 'now') || printf('%04d', id)
                    ELSE student_id
                END
                WHERE student_id IS NULL OR student_id = ''
            ''')

            conn.commit()
            print("تم إنشاء قاعدة البيانات بنجاح!")

        except sqlite3.Error as e:
            print(f"خطأ في إنشاء قاعدة البيانات: {e}")
            conn.rollback()
        finally:
            conn.close()

    def execute_query(self, query: str, params: tuple = ()) -> List[sqlite3.Row]:
        """تنفيذ استعلام SELECT وإرجاع النتائج"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute(query, params)
            return cursor.fetchall()
        finally:
            conn.close()

    def execute_update(self, query: str, params: tuple = ()) -> int:
        """تنفيذ استعلام INSERT/UPDATE/DELETE وإرجاع عدد الصفوف المتأثرة"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            return cursor.rowcount
        finally:
            conn.close()

    def get_last_insert_id(self, query: str, params: tuple = ()) -> int:
        """تنفيذ استعلام INSERT وإرجاع ID الصف المدرج"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            return cursor.lastrowid
        finally:
            conn.close()

    def backup_database(self, backup_path: str) -> bool:
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            import shutil
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"academy_backup_{timestamp}.db"
            full_backup_path = os.path.join(backup_path, backup_filename)

            # إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
            os.makedirs(backup_path, exist_ok=True)

            # نسخ ملف قاعدة البيانات
            shutil.copy2(self.db_path, full_backup_path)
            return True
        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False

# إنشاء مثيل عام من مدير قاعدة البيانات
db_manager = DatabaseManager()
