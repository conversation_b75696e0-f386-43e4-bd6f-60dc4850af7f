# -*- coding: utf-8 -*-
"""
نظام إدارة الأكاديمية - الملف الرئيسي
"""

import sys
import os

# التحقق من وجود PyQt5
try:
    from PyQt5.QtWidgets import QApplication, QSplashScreen, QMessageBox
    from PyQt5.QtCore import Qt, QTimer
    from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor
except ImportError:
    print("خطأ: PyQt5 غير مثبت!")
    print("يرجى تشغيل install_requirements.bat أولاً")
    input("اضغط Enter للخروج...")
    sys.exit(1)

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from ui.main_window import MainWindow
    from database import db_manager
except ImportError as e:
    print(f"خطأ في استيراد الملفات: {e}")
    print("تأكد من وجود جميع ملفات البرنامج")
    input("اضغط Enter للخروج...")
    sys.exit(1)

class SplashScreen(QSplashScreen):
    """شاشة البداية"""

    def __init__(self):
        # إنشاء صورة بسيطة لشاشة البداية
        pixmap = QPixmap(400, 300)
        pixmap.fill(QColor(46, 125, 50))  # لون أخضر

        # رسم النص على الصورة
        painter = QPainter(pixmap)
        painter.setPen(QColor(255, 255, 255))
        painter.setFont(QFont("Arial", 24, QFont.Bold))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, "نظام إدارة الأكاديمية\n\nجاري التحميل...")
        painter.end()

        super().__init__(pixmap, Qt.WindowStaysOnTopHint)
        self.setMask(pixmap.mask())

    def showMessage(self, message):
        """عرض رسالة على شاشة البداية"""
        super().showMessage(message, Qt.AlignBottom | Qt.AlignCenter, QColor(255, 255, 255))

def check_requirements():
    """فحص المتطلبات الأساسية"""
    try:
        # فحص قاعدة البيانات
        db_manager.init_database()
        return True
    except Exception as e:
        QMessageBox.critical(None, "خطأ في التهيئة",
                           f"حدث خطأ أثناء تهيئة قاعدة البيانات:\n{str(e)}")
        return False

def main():
    """الدالة الرئيسية للتطبيق"""
    # إنشاء التطبيق
    app = QApplication(sys.argv)

    # تعيين خصائص التطبيق
    app.setApplicationName("نظام إدارة الأكاديمية")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("أكاديمية التعلم")

    # تعيين الخط العربي
    font = QFont("Arial", 10)
    app.setFont(font)

    # تعيين اتجاه النص من اليمين لليسار
    app.setLayoutDirection(Qt.RightToLeft)

    # عرض شاشة البداية
    splash = SplashScreen()
    splash.show()

    # معالجة الأحداث لعرض شاشة البداية
    app.processEvents()

    # فحص المتطلبات
    splash.showMessage("فحص المتطلبات...")
    app.processEvents()

    if not check_requirements():
        splash.close()
        return 1

    # تحميل البيانات الأساسية
    splash.showMessage("تحميل البيانات...")
    app.processEvents()

    # محاكاة وقت التحميل
    QTimer.singleShot(1000, lambda: None)
    app.processEvents()

    # إنشاء النافذة الرئيسية
    splash.showMessage("إنشاء الواجهة...")
    app.processEvents()

    try:
        main_window = MainWindow()

        # إخفاء شاشة البداية وعرض النافذة الرئيسية
        splash.finish(main_window)
        main_window.show()

        # تشغيل التطبيق
        return app.exec_()

    except Exception as e:
        splash.close()
        QMessageBox.critical(None, "خطأ في التطبيق",
                           f"حدث خطأ أثناء تشغيل التطبيق:\n{str(e)}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
