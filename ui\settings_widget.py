# -*- coding: utf-8 -*-
"""
واجهة الإعدادات
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QLineEdit, QGroupBox, QFormLayout,
                             QSpinBox, QTextEdit, QFileDialog, QMessageBox,
                             QTabWidget, QCheckBox, QComboBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class SettingsWidget(QWidget):
    """واجهة الإعدادات"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_settings()
    
    def setup_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # عنوان الصفحة
        title_label = QLabel("إعدادات النظام")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2E7D32; padding: 10px;")
        layout.addWidget(title_label)
        
        # تبويبات الإعدادات
        self.settings_tabs = QTabWidget()
        
        # تبويب الإعدادات العامة
        general_tab = self.create_general_settings_tab()
        self.settings_tabs.addTab(general_tab, "الإعدادات العامة")
        
        # تبويب إعدادات النسخ الاحتياطي
        backup_tab = self.create_backup_settings_tab()
        self.settings_tabs.addTab(backup_tab, "النسخ الاحتياطي")
        
        # تبويب إدارة المستخدمين
        users_tab = self.create_users_settings_tab()
        self.settings_tabs.addTab(users_tab, "إدارة المستخدمين")
        
        layout.addWidget(self.settings_tabs)
        
        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        self.save_btn = QPushButton("حفظ الإعدادات")
        self.reset_btn = QPushButton("استعادة الافتراضي")
        
        self.save_btn.clicked.connect(self.save_settings)
        self.reset_btn.clicked.connect(self.reset_settings)
        
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.reset_btn)
        
        layout.addLayout(buttons_layout)
        
        # تطبيق الستايل
        self.apply_style()
    
    def create_general_settings_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # إعدادات الأكاديمية
        academy_group = QGroupBox("بيانات الأكاديمية")
        academy_layout = QFormLayout(academy_group)
        
        self.academy_name_edit = QLineEdit()
        self.academy_name_edit.setPlaceholderText("اسم الأكاديمية")
        academy_layout.addRow("اسم الأكاديمية:", self.academy_name_edit)
        
        self.academy_logo_btn = QPushButton("اختيار الشعار")
        self.academy_logo_btn.clicked.connect(self.choose_logo)
        academy_layout.addRow("شعار الأكاديمية:", self.academy_logo_btn)
        
        self.academy_address_edit = QTextEdit()
        self.academy_address_edit.setMaximumHeight(80)
        self.academy_address_edit.setPlaceholderText("عنوان الأكاديمية")
        academy_layout.addRow("العنوان:", self.academy_address_edit)
        
        layout.addWidget(academy_group)
        
        # إعدادات الحصص
        sessions_group = QGroupBox("إعدادات الحصص")
        sessions_layout = QFormLayout(sessions_group)
        
        self.session_duration_spin = QSpinBox()
        self.session_duration_spin.setRange(15, 300)
        self.session_duration_spin.setSuffix(" دقيقة")
        self.session_duration_spin.setValue(60)
        sessions_layout.addRow("مدة الحصة:", self.session_duration_spin)
        
        self.default_link_edit = QLineEdit()
        self.default_link_edit.setPlaceholderText("https://zoom.us/j/meeting")
        sessions_layout.addRow("رابط الجلسة الافتراضي:", self.default_link_edit)
        
        layout.addWidget(sessions_group)
        
        # إعدادات العملة
        currency_group = QGroupBox("إعدادات العملة")
        currency_layout = QFormLayout(currency_group)
        
        self.currency_combo = QComboBox()
        self.currency_combo.addItems(["ريال", "دولار", "يورو", "دينار"])
        currency_layout.addRow("العملة:", self.currency_combo)
        
        layout.addWidget(currency_group)
        
        layout.addStretch()
        return tab
    
    def create_backup_settings_tab(self):
        """إنشاء تبويب النسخ الاحتياطي"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # إعدادات النسخ الاحتياطي
        backup_group = QGroupBox("إعدادات النسخ الاحتياطي")
        backup_layout = QFormLayout(backup_group)
        
        # مسار النسخ الاحتياطي
        backup_path_layout = QHBoxLayout()
        self.backup_path_edit = QLineEdit()
        self.backup_path_edit.setPlaceholderText("./backups")
        self.backup_path_btn = QPushButton("تصفح")
        self.backup_path_btn.clicked.connect(self.choose_backup_path)
        
        backup_path_layout.addWidget(self.backup_path_edit)
        backup_path_layout.addWidget(self.backup_path_btn)
        
        backup_layout.addRow("مسار النسخ الاحتياطي:", backup_path_layout)
        
        # النسخ الاحتياطي التلقائي
        self.auto_backup_check = QCheckBox("تفعيل النسخ الاحتياطي التلقائي")
        backup_layout.addRow("", self.auto_backup_check)
        
        layout.addWidget(backup_group)
        
        # عمليات النسخ الاحتياطي
        operations_group = QGroupBox("عمليات النسخ الاحتياطي")
        operations_layout = QVBoxLayout(operations_group)
        
        self.create_backup_btn = QPushButton("إنشاء نسخة احتياطية الآن")
        self.create_backup_btn.clicked.connect(self.create_backup_now)
        operations_layout.addWidget(self.create_backup_btn)
        
        self.restore_backup_btn = QPushButton("استعادة من نسخة احتياطية")
        self.restore_backup_btn.clicked.connect(self.restore_backup)
        operations_layout.addWidget(self.restore_backup_btn)
        
        layout.addWidget(operations_group)
        
        layout.addStretch()
        return tab
    
    def create_users_settings_tab(self):
        """إنشاء تبويب إدارة المستخدمين"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # رسالة مؤقتة
        info_label = QLabel("ستتم إضافة إدارة المستخدمين والصلاحيات قريباً...")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #666; font-size: 14px; padding: 50px;")
        layout.addWidget(info_label)
        
        return tab
    
    def choose_logo(self):
        """اختيار شعار الأكاديمية"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختيار شعار الأكاديمية", "", 
            "Image Files (*.png *.jpg *.jpeg *.bmp *.gif)"
        )
        if file_path:
            QMessageBox.information(self, "تم", f"تم اختيار الشعار: {file_path}")
    
    def choose_backup_path(self):
        """اختيار مسار النسخ الاحتياطي"""
        folder_path = QFileDialog.getExistingDirectory(self, "اختيار مجلد النسخ الاحتياطي")
        if folder_path:
            self.backup_path_edit.setText(folder_path)
    
    def create_backup_now(self):
        """إنشاء نسخة احتياطية فورية"""
        QMessageBox.information(self, "قريباً", "ستتم إضافة هذه الميزة قريباً!")
    
    def restore_backup(self):
        """استعادة من نسخة احتياطية"""
        QMessageBox.information(self, "قريباً", "ستتم إضافة هذه الميزة قريباً!")
    
    def load_settings(self):
        """تحميل الإعدادات من قاعدة البيانات"""
        # سيتم تنفيذ هذا لاحقاً
        self.academy_name_edit.setText("أكاديمية التعلم")
        self.session_duration_spin.setValue(60)
        self.default_link_edit.setText("https://zoom.us/j/meeting")
        self.backup_path_edit.setText("./backups")
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # سيتم تنفيذ حفظ الإعدادات في قاعدة البيانات لاحقاً
            QMessageBox.information(self, "نجح", "تم حفظ الإعدادات بنجاح!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الإعدادات: {str(e)}")
    
    def reset_settings(self):
        """استعادة الإعدادات الافتراضية"""
        reply = QMessageBox.question(self, "تأكيد", 
                                   "هل تريد استعادة الإعدادات الافتراضية؟",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.load_settings()
            QMessageBox.information(self, "تم", "تم استعادة الإعدادات الافتراضية!")
    
    def apply_style(self):
        """تطبيق الستايل على الواجهة"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2E7D32;
            }
            
            QLineEdit, QTextEdit, QSpinBox, QComboBox {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 4px;
                font-size: 12px;
            }
            
            QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QComboBox:focus {
                border-color: #4CAF50;
            }
            
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #45a049;
            }
            
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
                border-radius: 5px;
            }
            
            QTabBar::tab {
                background-color: #e1e1e1;
                border: 1px solid #c0c0c0;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            
            QTabBar::tab:selected {
                background-color: #4CAF50;
                color: white;
            }
        """)
