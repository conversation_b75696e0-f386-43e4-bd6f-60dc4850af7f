# -*- coding: utf-8 -*-
"""
واجهة إدارة المجموعات
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QComboBox, QDateEdit, QDialog, QFormLayout, 
                             QDialogButtonBox, QMessageBox, QHeaderView, QSpinBox,
                             QTextEdit)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

from models import Group, Course, Teacher
from datetime import date

class GroupDialog(QDialog):
    """نافذة إضافة/تعديل مجموعة"""
    
    def __init__(self, group=None, parent=None):
        super().__init__(parent)
        self.group = group
        self.setWindowTitle("إضافة مجموعة جديدة" if group is None else "تعديل بيانات المجموعة")
        self.setModal(True)
        self.setFixedSize(500, 450)
        
        self.setup_ui()
        self.load_courses_and_teachers()
        
        if self.group:
            self.load_group_data()
    
    def setup_ui(self):
        """إنشاء واجهة النافذة"""
        layout = QVBoxLayout(self)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # اسم المجموعة
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم المجموعة")
        form_layout.addRow("اسم المجموعة:", self.name_edit)
        
        # الكورس
        self.course_combo = QComboBox()
        form_layout.addRow("الكورس:", self.course_combo)
        
        # المعلم
        self.teacher_combo = QComboBox()
        form_layout.addRow("المعلم:", self.teacher_combo)
        
        # تاريخ البداية
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate())
        self.start_date_edit.setCalendarPopup(True)
        form_layout.addRow("تاريخ البداية:", self.start_date_edit)
        
        # تاريخ النهاية
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate().addMonths(3))
        self.end_date_edit.setCalendarPopup(True)
        form_layout.addRow("تاريخ النهاية:", self.end_date_edit)
        
        # الجدول الزمني
        self.schedule_edit = QTextEdit()
        self.schedule_edit.setMaximumHeight(80)
        self.schedule_edit.setPlaceholderText("مثال: الأحد والثلاثاء 4:00 - 6:00 مساءً")
        form_layout.addRow("الجدول الزمني:", self.schedule_edit)
        
        # الحد الأقصى للطلاب
        self.max_students_spin = QSpinBox()
        self.max_students_spin.setRange(1, 100)
        self.max_students_spin.setValue(20)
        form_layout.addRow("الحد الأقصى للطلاب:", self.max_students_spin)
        
        layout.addLayout(form_layout)
        
        # أزرار الحفظ والإلغاء
        button_box = QDialogButtonBox(QDialogButtonBox.Save | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.save_group)
        button_box.rejected.connect(self.reject)
        
        # تعريب النصوص
        button_box.button(QDialogButtonBox.Save).setText("حفظ")
        button_box.button(QDialogButtonBox.Cancel).setText("إلغاء")
        
        layout.addWidget(button_box)
    
    def load_courses_and_teachers(self):
        """تحميل قوائم الكورسات والمعلمين"""
        try:
            # تحميل الكورسات
            courses = Course.get_all()
            self.course_combo.clear()
            for course in courses:
                self.course_combo.addItem(course.name, course.id)
            
            # تحميل المعلمين
            teachers = Teacher.get_all()
            self.teacher_combo.clear()
            for teacher in teachers:
                self.teacher_combo.addItem(teacher.name, teacher.id)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")
    
    def load_group_data(self):
        """تحميل بيانات المجموعة للتعديل"""
        if self.group:
            self.name_edit.setText(self.group.name)
            
            # تحديد الكورس
            course_index = self.course_combo.findData(self.group.course_id)
            if course_index >= 0:
                self.course_combo.setCurrentIndex(course_index)
            
            # تحديد المعلم
            teacher_index = self.teacher_combo.findData(self.group.teacher_id)
            if teacher_index >= 0:
                self.teacher_combo.setCurrentIndex(teacher_index)
            
            # تواريخ البداية والنهاية
            if self.group.start_date:
                qdate = QDate(self.group.start_date.year, 
                             self.group.start_date.month, 
                             self.group.start_date.day)
                self.start_date_edit.setDate(qdate)
            
            if self.group.end_date:
                qdate = QDate(self.group.end_date.year, 
                             self.group.end_date.month, 
                             self.group.end_date.day)
                self.end_date_edit.setDate(qdate)
            
            self.schedule_edit.setPlainText(self.group.schedule)
            self.max_students_spin.setValue(self.group.max_students)
    
    def save_group(self):
        """حفظ بيانات المجموعة"""
        # التحقق من صحة البيانات
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يجب إدخال اسم المجموعة!")
            return
        
        if self.course_combo.currentData() is None:
            QMessageBox.warning(self, "خطأ", "يجب اختيار كورس!")
            return
        
        if self.teacher_combo.currentData() is None:
            QMessageBox.warning(self, "خطأ", "يجب اختيار معلم!")
            return
        
        try:
            # إنشاء أو تحديث كائن المجموعة
            if self.group is None:
                self.group = Group()
            
            self.group.name = self.name_edit.text().strip()
            self.group.course_id = self.course_combo.currentData()
            self.group.teacher_id = self.teacher_combo.currentData()
            
            # تحويل التواريخ
            start_qdate = self.start_date_edit.date()
            self.group.start_date = date(start_qdate.year(), start_qdate.month(), start_qdate.day())
            
            end_qdate = self.end_date_edit.date()
            self.group.end_date = date(end_qdate.year(), end_qdate.month(), end_qdate.day())
            
            self.group.schedule = self.schedule_edit.toPlainText().strip()
            self.group.max_students = self.max_students_spin.value()
            
            # حفظ في قاعدة البيانات
            self.group.save()
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحفظ: {str(e)}")

class GroupsWidget(QWidget):
    """واجهة إدارة المجموعات"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_groups()
    
    def setup_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # شريط البحث والأزرار
        top_layout = QHBoxLayout()
        
        # البحث
        search_label = QLabel("البحث:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالاسم...")
        self.search_edit.textChanged.connect(self.search_groups)
        
        # أزرار العمليات
        self.add_btn = QPushButton("إضافة مجموعة")
        self.edit_btn = QPushButton("تعديل")
        self.delete_btn = QPushButton("حذف")
        self.refresh_btn = QPushButton("تحديث")
        self.manage_students_btn = QPushButton("إدارة الطلاب")
        
        # ربط الأزرار بالوظائف
        self.add_btn.clicked.connect(self.add_group)
        self.edit_btn.clicked.connect(self.edit_group)
        self.delete_btn.clicked.connect(self.delete_group)
        self.refresh_btn.clicked.connect(self.load_groups)
        self.manage_students_btn.clicked.connect(self.manage_group_students)
        
        # تعطيل أزرار التعديل والحذف في البداية
        self.edit_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)
        self.manage_students_btn.setEnabled(False)
        
        top_layout.addWidget(search_label)
        top_layout.addWidget(self.search_edit)
        top_layout.addStretch()
        top_layout.addWidget(self.add_btn)
        top_layout.addWidget(self.edit_btn)
        top_layout.addWidget(self.delete_btn)
        top_layout.addWidget(self.manage_students_btn)
        top_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(top_layout)
        
        # جدول المجموعات
        self.groups_table = QTableWidget()
        self.groups_table.setColumnCount(7)
        self.groups_table.setHorizontalHeaderLabels([
            "الرقم", "اسم المجموعة", "الكورس", "المعلم", 
            "تاريخ البداية", "تاريخ النهاية", "الحد الأقصى"
        ])
        
        # تخصيص الجدول
        header = self.groups_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        # تحديد عرض الأعمدة
        self.groups_table.setColumnWidth(0, 60)   # الرقم
        self.groups_table.setColumnWidth(1, 150)  # اسم المجموعة
        self.groups_table.setColumnWidth(2, 150)  # الكورس
        self.groups_table.setColumnWidth(3, 150)  # المعلم
        self.groups_table.setColumnWidth(4, 100)  # تاريخ البداية
        self.groups_table.setColumnWidth(5, 100)  # تاريخ النهاية
        self.groups_table.setColumnWidth(6, 80)   # الحد الأقصى
        
        # ربط حدث تحديد الصف
        self.groups_table.selectionModel().selectionChanged.connect(self.on_selection_changed)
        self.groups_table.doubleClicked.connect(self.edit_group)
        
        layout.addWidget(self.groups_table)
        
        # تطبيق الستايل
        self.apply_style()
    
    def apply_style(self):
        """تطبيق الستايل على الواجهة"""
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #4CAF50;
                selection-color: white;
            }
            
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e0e0e0;
            }
            
            QHeaderView::section {
                background-color: #2E7D32;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
            
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 4px;
                font-size: 12px;
            }
            
            QLineEdit:focus {
                border-color: #4CAF50;
            }
        """)
    
    def load_groups(self):
        """تحميل قائمة المجموعات"""
        try:
            groups = Group.get_all()
            self.populate_table(groups)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")
    
    def populate_table(self, groups):
        """ملء الجدول بالمجموعات"""
        self.groups_table.setRowCount(len(groups))
        
        for row, group in enumerate(groups):
            self.groups_table.setItem(row, 0, QTableWidgetItem(str(group.id)))
            self.groups_table.setItem(row, 1, QTableWidgetItem(group.name))
            self.groups_table.setItem(row, 2, QTableWidgetItem(getattr(group, 'course_name', '')))
            self.groups_table.setItem(row, 3, QTableWidgetItem(getattr(group, 'teacher_name', '')))
            
            start_date_str = group.start_date.strftime("%Y-%m-%d") if group.start_date else ""
            self.groups_table.setItem(row, 4, QTableWidgetItem(start_date_str))
            
            end_date_str = group.end_date.strftime("%Y-%m-%d") if group.end_date else ""
            self.groups_table.setItem(row, 5, QTableWidgetItem(end_date_str))
            
            self.groups_table.setItem(row, 6, QTableWidgetItem(str(group.max_students)))
            
            # حفظ معرف المجموعة في البيانات المخفية
            self.groups_table.item(row, 0).setData(Qt.UserRole, group.id)
    
    def search_groups(self):
        """البحث عن المجموعات"""
        keyword = self.search_edit.text().strip()
        if keyword:
            try:
                all_groups = Group.get_all()
                filtered_groups = [
                    group for group in all_groups
                    if keyword.lower() in group.name.lower()
                ]
                self.populate_table(filtered_groups)
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث: {str(e)}")
        else:
            self.load_groups()
    
    def on_selection_changed(self):
        """التعامل مع تغيير التحديد في الجدول"""
        has_selection = len(self.groups_table.selectionModel().selectedRows()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
        self.manage_students_btn.setEnabled(has_selection)
    
    def add_group(self):
        """إضافة مجموعة جديدة"""
        dialog = GroupDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_groups()
            QMessageBox.information(self, "نجح", "تم إضافة المجموعة بنجاح!")
    
    def edit_group(self):
        """تعديل مجموعة محددة"""
        current_row = self.groups_table.currentRow()
        if current_row >= 0:
            group_id = self.groups_table.item(current_row, 0).data(Qt.UserRole)
            # سنحتاج لإنشاء دالة get_by_id في نموذج Group
            QMessageBox.information(self, "قريباً", "ستتم إضافة هذه الميزة قريباً!")
    
    def delete_group(self):
        """حذف مجموعة محددة"""
        current_row = self.groups_table.currentRow()
        if current_row >= 0:
            group_name = self.groups_table.item(current_row, 1).text()
            
            reply = QMessageBox.question(self, "تأكيد الحذف", 
                                       f"هل تريد حذف المجموعة '{group_name}'؟\n"
                                       "هذا الإجراء لا يمكن التراجع عنه!",
                                       QMessageBox.Yes | QMessageBox.No,
                                       QMessageBox.No)
            
            if reply == QMessageBox.Yes:
                QMessageBox.information(self, "قريباً", "ستتم إضافة هذه الميزة قريباً!")
    
    def manage_group_students(self):
        """إدارة طلاب المجموعة"""
        QMessageBox.information(self, "قريباً", "ستتم إضافة هذه الميزة قريباً!")
