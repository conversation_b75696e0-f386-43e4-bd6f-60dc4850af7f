# -*- coding: utf-8 -*-
"""
واجهة التقارير
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QComboBox, QDateEdit, QGroupBox,
                             QGridLayout, QTextEdit, QMessageBox, QFrame)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

class ReportsWidget(QWidget):
    """واجهة التقارير"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # عنوان الصفحة
        title_label = QLabel("التقارير والإحصائيات")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2E7D32; padding: 10px;")
        layout.addWidget(title_label)
        
        # تقسيم الصفحة إلى أقسام
        main_layout = QHBoxLayout()
        
        # القسم الأيسر - تقارير الطلاب
        students_group = QGroupBox("تقارير الطلاب")
        students_layout = QVBoxLayout(students_group)
        
        self.students_report_btn = QPushButton("تقرير جميع الطلاب")
        self.students_by_group_btn = QPushButton("تقرير الطلاب حسب المجموعة")
        self.students_attendance_btn = QPushButton("تقرير حضور الطلاب")
        
        students_layout.addWidget(self.students_report_btn)
        students_layout.addWidget(self.students_by_group_btn)
        students_layout.addWidget(self.students_attendance_btn)
        students_layout.addStretch()
        
        # القسم الأوسط - تقارير المالية
        finance_group = QGroupBox("التقارير المالية")
        finance_layout = QVBoxLayout(finance_group)
        
        self.payments_report_btn = QPushButton("تقرير الدفعات")
        self.monthly_income_btn = QPushButton("الدخل الشهري")
        self.outstanding_payments_btn = QPushButton("المدفوعات المتأخرة")
        
        finance_layout.addWidget(self.payments_report_btn)
        finance_layout.addWidget(self.monthly_income_btn)
        finance_layout.addWidget(self.outstanding_payments_btn)
        finance_layout.addStretch()
        
        # القسم الأيمن - تقارير المعلمين والكورسات
        courses_group = QGroupBox("تقارير الكورسات والمعلمين")
        courses_layout = QVBoxLayout(courses_group)
        
        self.courses_report_btn = QPushButton("تقرير الكورسات")
        self.teachers_report_btn = QPushButton("تقرير المعلمين")
        self.groups_report_btn = QPushButton("تقرير المجموعات")
        
        courses_layout.addWidget(self.courses_report_btn)
        courses_layout.addWidget(self.teachers_report_btn)
        courses_layout.addWidget(self.groups_report_btn)
        courses_layout.addStretch()
        
        main_layout.addWidget(students_group)
        main_layout.addWidget(finance_group)
        main_layout.addWidget(courses_group)
        
        layout.addLayout(main_layout)
        
        # قسم فلاتر التقارير
        filters_group = QGroupBox("فلاتر التقارير")
        filters_layout = QGridLayout(filters_group)
        
        # فترة التقرير
        filters_layout.addWidget(QLabel("من تاريخ:"), 0, 0)
        self.from_date_edit = QDateEdit()
        self.from_date_edit.setDate(QDate.currentDate().addMonths(-1))
        self.from_date_edit.setCalendarPopup(True)
        filters_layout.addWidget(self.from_date_edit, 0, 1)
        
        filters_layout.addWidget(QLabel("إلى تاريخ:"), 0, 2)
        self.to_date_edit = QDateEdit()
        self.to_date_edit.setDate(QDate.currentDate())
        self.to_date_edit.setCalendarPopup(True)
        filters_layout.addWidget(self.to_date_edit, 0, 3)
        
        # نوع التصدير
        filters_layout.addWidget(QLabel("تصدير كـ:"), 1, 0)
        self.export_type_combo = QComboBox()
        self.export_type_combo.addItems(["PDF", "Excel"])
        filters_layout.addWidget(self.export_type_combo, 1, 1)
        
        # زر التصدير
        self.export_btn = QPushButton("تصدير التقرير")
        self.export_btn.setStyleSheet("background-color: #FF9800; color: white;")
        filters_layout.addWidget(self.export_btn, 1, 2, 1, 2)
        
        layout.addWidget(filters_group)
        
        # منطقة معاينة التقرير
        preview_group = QGroupBox("معاينة التقرير")
        preview_layout = QVBoxLayout(preview_group)
        
        self.report_preview = QTextEdit()
        self.report_preview.setReadOnly(True)
        self.report_preview.setPlaceholderText("اختر نوع التقرير لعرض المعاينة هنا...")
        preview_layout.addWidget(self.report_preview)
        
        layout.addWidget(preview_group)
        
        # ربط الأزرار بالوظائف (مؤقتاً)
        self.connect_buttons()
        
        # تطبيق الستايل
        self.apply_style()
    
    def connect_buttons(self):
        """ربط الأزرار بالوظائف"""
        # تقارير الطلاب
        self.students_report_btn.clicked.connect(lambda: self.show_temp_message("تقرير الطلاب"))
        self.students_by_group_btn.clicked.connect(lambda: self.show_temp_message("تقرير الطلاب حسب المجموعة"))
        self.students_attendance_btn.clicked.connect(lambda: self.show_temp_message("تقرير حضور الطلاب"))
        
        # التقارير المالية
        self.payments_report_btn.clicked.connect(lambda: self.show_temp_message("تقرير الدفعات"))
        self.monthly_income_btn.clicked.connect(lambda: self.show_temp_message("الدخل الشهري"))
        self.outstanding_payments_btn.clicked.connect(lambda: self.show_temp_message("المدفوعات المتأخرة"))
        
        # تقارير الكورسات والمعلمين
        self.courses_report_btn.clicked.connect(lambda: self.show_temp_message("تقرير الكورسات"))
        self.teachers_report_btn.clicked.connect(lambda: self.show_temp_message("تقرير المعلمين"))
        self.groups_report_btn.clicked.connect(lambda: self.show_temp_message("تقرير المجموعات"))
        
        # التصدير
        self.export_btn.clicked.connect(lambda: self.show_temp_message("تصدير التقرير"))
    
    def show_temp_message(self, report_type):
        """عرض رسالة مؤقتة"""
        QMessageBox.information(self, "قريباً", f"ستتم إضافة {report_type} قريباً!")
        self.report_preview.setPlainText(f"معاينة {report_type}\n\nستتم إضافة هذه الميزة قريباً...")
    
    def apply_style(self):
        """تطبيق الستايل على الواجهة"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2E7D32;
            }
            
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
                margin: 2px;
            }
            
            QPushButton:hover {
                background-color: #45a049;
            }
            
            QDateEdit, QComboBox {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 4px;
                font-size: 12px;
            }
            
            QTextEdit {
                border: 2px solid #ddd;
                border-radius: 4px;
                padding: 10px;
                font-family: 'Courier New', monospace;
            }
        """)
