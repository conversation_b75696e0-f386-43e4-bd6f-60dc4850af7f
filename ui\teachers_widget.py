# -*- coding: utf-8 -*-
"""
واجهة إدارة المعلمين
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QDialog, QFormLayout, QDialogButtonBox, QMessageBox, 
                             QHeaderView, QDoubleSpinBox, QTextEdit)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from models import Teacher

class TeacherDialog(QDialog):
    """نافذة إضافة/تعديل معلم"""
    
    def __init__(self, teacher=None, parent=None):
        super().__init__(parent)
        self.teacher = teacher
        self.setWindowTitle("إضافة معلم جديد" if teacher is None else "تعديل بيانات المعلم")
        self.setModal(True)
        self.setFixedSize(400, 300)
        
        self.setup_ui()
        
        if self.teacher:
            self.load_teacher_data()
    
    def setup_ui(self):
        """إنشاء واجهة النافذة"""
        layout = QVBoxLayout(self)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # الاسم
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم المعلم")
        form_layout.addRow("الاسم:", self.name_edit)
        
        # رقم الهاتف
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("رقم هاتف المعلم")
        form_layout.addRow("رقم الهاتف:", self.phone_edit)
        
        # المرتب
        self.salary_spin = QDoubleSpinBox()
        self.salary_spin.setRange(0, 999999)
        self.salary_spin.setSuffix(" ريال")
        self.salary_spin.setDecimals(2)
        form_layout.addRow("المرتب:", self.salary_spin)
        
        # التخصص
        self.specialization_edit = QTextEdit()
        self.specialization_edit.setMaximumHeight(80)
        self.specialization_edit.setPlaceholderText("تخصص المعلم")
        form_layout.addRow("التخصص:", self.specialization_edit)
        
        layout.addLayout(form_layout)
        
        # أزرار الحفظ والإلغاء
        button_box = QDialogButtonBox(QDialogButtonBox.Save | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.save_teacher)
        button_box.rejected.connect(self.reject)
        
        # تعريب النصوص
        button_box.button(QDialogButtonBox.Save).setText("حفظ")
        button_box.button(QDialogButtonBox.Cancel).setText("إلغاء")
        
        layout.addWidget(button_box)
    
    def load_teacher_data(self):
        """تحميل بيانات المعلم للتعديل"""
        if self.teacher:
            self.name_edit.setText(self.teacher.name)
            self.phone_edit.setText(self.teacher.phone)
            self.salary_spin.setValue(self.teacher.salary)
            self.specialization_edit.setPlainText(self.teacher.specialization)
    
    def save_teacher(self):
        """حفظ بيانات المعلم"""
        # التحقق من صحة البيانات
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يجب إدخال اسم المعلم!")
            return
        
        try:
            # إنشاء أو تحديث كائن المعلم
            if self.teacher is None:
                self.teacher = Teacher()
            
            self.teacher.name = self.name_edit.text().strip()
            self.teacher.phone = self.phone_edit.text().strip()
            self.teacher.salary = self.salary_spin.value()
            self.teacher.specialization = self.specialization_edit.toPlainText().strip()
            
            # حفظ في قاعدة البيانات
            self.teacher.save()
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحفظ: {str(e)}")

class TeachersWidget(QWidget):
    """واجهة إدارة المعلمين"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_teachers()
    
    def setup_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # شريط البحث والأزرار
        top_layout = QHBoxLayout()
        
        # البحث
        search_label = QLabel("البحث:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالاسم أو رقم الهاتف...")
        self.search_edit.textChanged.connect(self.search_teachers)
        
        # أزرار العمليات
        self.add_btn = QPushButton("إضافة معلم")
        self.edit_btn = QPushButton("تعديل")
        self.delete_btn = QPushButton("حذف")
        self.refresh_btn = QPushButton("تحديث")
        
        # ربط الأزرار بالوظائف
        self.add_btn.clicked.connect(self.add_teacher)
        self.edit_btn.clicked.connect(self.edit_teacher)
        self.delete_btn.clicked.connect(self.delete_teacher)
        self.refresh_btn.clicked.connect(self.load_teachers)
        
        # تعطيل أزرار التعديل والحذف في البداية
        self.edit_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)
        
        top_layout.addWidget(search_label)
        top_layout.addWidget(self.search_edit)
        top_layout.addStretch()
        top_layout.addWidget(self.add_btn)
        top_layout.addWidget(self.edit_btn)
        top_layout.addWidget(self.delete_btn)
        top_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(top_layout)
        
        # جدول المعلمين
        self.teachers_table = QTableWidget()
        self.teachers_table.setColumnCount(5)
        self.teachers_table.setHorizontalHeaderLabels([
            "الرقم", "الاسم", "رقم الهاتف", "المرتب", "التخصص"
        ])
        
        # تخصيص الجدول
        header = self.teachers_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        # تحديد عرض الأعمدة
        self.teachers_table.setColumnWidth(0, 60)   # الرقم
        self.teachers_table.setColumnWidth(1, 200)  # الاسم
        self.teachers_table.setColumnWidth(2, 150)  # رقم الهاتف
        self.teachers_table.setColumnWidth(3, 120)  # المرتب
        
        # ربط حدث تحديد الصف
        self.teachers_table.selectionModel().selectionChanged.connect(self.on_selection_changed)
        self.teachers_table.doubleClicked.connect(self.edit_teacher)
        
        layout.addWidget(self.teachers_table)
        
        # تطبيق الستايل
        self.apply_style()
    
    def apply_style(self):
        """تطبيق الستايل على الواجهة"""
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #4CAF50;
                selection-color: white;
            }
            
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e0e0e0;
            }
            
            QHeaderView::section {
                background-color: #2E7D32;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
            
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 4px;
                font-size: 12px;
            }
            
            QLineEdit:focus {
                border-color: #4CAF50;
            }
        """)
    
    def load_teachers(self):
        """تحميل قائمة المعلمين"""
        try:
            teachers = Teacher.get_all()
            self.populate_table(teachers)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")
    
    def populate_table(self, teachers):
        """ملء الجدول بالمعلمين"""
        self.teachers_table.setRowCount(len(teachers))
        
        for row, teacher in enumerate(teachers):
            self.teachers_table.setItem(row, 0, QTableWidgetItem(str(teacher.id)))
            self.teachers_table.setItem(row, 1, QTableWidgetItem(teacher.name))
            self.teachers_table.setItem(row, 2, QTableWidgetItem(teacher.phone))
            self.teachers_table.setItem(row, 3, QTableWidgetItem(f"{teacher.salary:.2f} ريال"))
            self.teachers_table.setItem(row, 4, QTableWidgetItem(teacher.specialization))
            
            # حفظ معرف المعلم في البيانات المخفية
            self.teachers_table.item(row, 0).setData(Qt.UserRole, teacher.id)
    
    def search_teachers(self):
        """البحث عن المعلمين"""
        keyword = self.search_edit.text().strip()
        if keyword:
            try:
                # البحث في الاسم والهاتف
                all_teachers = Teacher.get_all()
                filtered_teachers = [
                    teacher for teacher in all_teachers
                    if keyword.lower() in teacher.name.lower() or 
                       keyword in teacher.phone
                ]
                self.populate_table(filtered_teachers)
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث: {str(e)}")
        else:
            self.load_teachers()
    
    def on_selection_changed(self):
        """التعامل مع تغيير التحديد في الجدول"""
        has_selection = len(self.teachers_table.selectionModel().selectedRows()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
    
    def add_teacher(self):
        """إضافة معلم جديد"""
        dialog = TeacherDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_teachers()
            QMessageBox.information(self, "نجح", "تم إضافة المعلم بنجاح!")
    
    def edit_teacher(self):
        """تعديل معلم محدد"""
        current_row = self.teachers_table.currentRow()
        if current_row >= 0:
            teacher_id = self.teachers_table.item(current_row, 0).data(Qt.UserRole)
            teacher = Teacher.get_by_id(teacher_id)
            
            if teacher:
                dialog = TeacherDialog(teacher, parent=self)
                if dialog.exec_() == QDialog.Accepted:
                    self.load_teachers()
                    QMessageBox.information(self, "نجح", "تم تحديث بيانات المعلم بنجاح!")
    
    def delete_teacher(self):
        """حذف معلم محدد"""
        current_row = self.teachers_table.currentRow()
        if current_row >= 0:
            teacher_name = self.teachers_table.item(current_row, 1).text()
            
            reply = QMessageBox.question(self, "تأكيد الحذف", 
                                       f"هل تريد حذف المعلم '{teacher_name}'؟\n"
                                       "هذا الإجراء لا يمكن التراجع عنه!",
                                       QMessageBox.Yes | QMessageBox.No,
                                       QMessageBox.No)
            
            if reply == QMessageBox.Yes:
                try:
                    teacher_id = self.teachers_table.item(current_row, 0).data(Qt.UserRole)
                    teacher = Teacher.get_by_id(teacher_id)
                    
                    if teacher and teacher.delete():
                        self.load_teachers()
                        QMessageBox.information(self, "نجح", "تم حذف المعلم بنجاح!")
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في حذف المعلم!")
                        
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")
