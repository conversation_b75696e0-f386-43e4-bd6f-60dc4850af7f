نظام إدارة الأكاديمية - تعليمات التشغيل السريعة
================================================

1. التثبيت الأولي:
   - تأكد من تثبيت Python 3.7 أو أحدث
   - شغل ملف install_requirements.bat لتثبيت المتطلبات

2. تشغيل البرنامج:
   - شغل ملف run.bat
   - أو استخدم الأمر: python main.py

3. الاستخدام:
   - ابدأ بإضافة المعلمين من تبويب "المعلمين"
   - أضف الكورسات من تبويب "الكورسات"
   - أضف الطلاب من تبويب "الطلاب"
   - أنشئ المجموعات من تبويب "المجموعات"

4. النسخ الاحتياطي:
   - من قائمة "ملف" اختر "إنشاء نسخة احتياطية"
   - يتم حفظ النسخ في مجلد backups/

5. تجميع البرنامج كملف EXE:
   - شغل ملف build_exe.bat
   - ستجد الملف التنفيذي في مجلد dist/

6. ملاحظات مهمة:
   - ملف قاعدة البيانات: academy.db
   - لا تحذف هذا الملف لتجنب فقدان البيانات
   - قم بعمل نسخ احتياطية دورية

7. المشاكل الشائعة:
   - إذا لم يعمل البرنامج، تأكد من تثبيت المتطلبات
   - إذا ظهرت رسالة خطأ، تأكد من وجود Python في PATH

للدعم: راجع ملف README.md للتفاصيل الكاملة
