# -*- coding: utf-8 -*-
"""
واجهة إدارة الكورسات
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QComboBox, QDialog, QFormLayout, QDialogButtonBox, 
                             QMessageBox, QHeaderView, QDoubleSpinBox, QSpinBox,
                             QTextEdit)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from models import Course

class CourseDialog(QDialog):
    """نافذة إضافة/تعديل كورس"""
    
    def __init__(self, course=None, parent=None):
        super().__init__(parent)
        self.course = course
        self.setWindowTitle("إضافة كورس جديد" if course is None else "تعديل بيانات الكورس")
        self.setModal(True)
        self.setFixedSize(500, 400)
        
        self.setup_ui()
        
        if self.course:
            self.load_course_data()
    
    def setup_ui(self):
        """إنشاء واجهة النافذة"""
        layout = QVBoxLayout(self)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # اسم الكورس
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم الكورس")
        form_layout.addRow("اسم الكورس:", self.name_edit)
        
        # السعر
        self.price_spin = QDoubleSpinBox()
        self.price_spin.setRange(0, 999999)
        self.price_spin.setSuffix(" ريال")
        self.price_spin.setDecimals(2)
        form_layout.addRow("السعر:", self.price_spin)
        
        # عدد الحصص
        self.sessions_spin = QSpinBox()
        self.sessions_spin.setRange(1, 1000)
        self.sessions_spin.setValue(10)
        form_layout.addRow("عدد الحصص:", self.sessions_spin)
        
        # نوع الكورس
        self.type_combo = QComboBox()
        self.type_combo.addItems(["حضوري", "أونلاين"])
        self.type_combo.currentTextChanged.connect(self.on_type_changed)
        form_layout.addRow("نوع الكورس:", self.type_combo)
        
        # رابط الجلسة (للكورسات الأونلاين)
        self.session_link_edit = QLineEdit()
        self.session_link_edit.setPlaceholderText("رابط الجلسة الأونلاين")
        self.session_link_edit.setEnabled(False)
        form_layout.addRow("رابط الجلسة:", self.session_link_edit)
        
        # الوصف
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        self.description_edit.setPlaceholderText("وصف الكورس")
        form_layout.addRow("الوصف:", self.description_edit)
        
        layout.addLayout(form_layout)
        
        # أزرار الحفظ والإلغاء
        button_box = QDialogButtonBox(QDialogButtonBox.Save | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.save_course)
        button_box.rejected.connect(self.reject)
        
        # تعريب النصوص
        button_box.button(QDialogButtonBox.Save).setText("حفظ")
        button_box.button(QDialogButtonBox.Cancel).setText("إلغاء")
        
        layout.addWidget(button_box)
    
    def on_type_changed(self, course_type):
        """التعامل مع تغيير نوع الكورس"""
        self.session_link_edit.setEnabled(course_type == "أونلاين")
        if course_type == "حضوري":
            self.session_link_edit.clear()
    
    def load_course_data(self):
        """تحميل بيانات الكورس للتعديل"""
        if self.course:
            self.name_edit.setText(self.course.name)
            self.price_spin.setValue(self.course.price)
            self.sessions_spin.setValue(self.course.sessions_count)
            
            type_index = 0 if self.course.type == "حضوري" else 1
            self.type_combo.setCurrentIndex(type_index)
            
            self.session_link_edit.setText(self.course.session_link)
            self.description_edit.setPlainText(self.course.description)
    
    def save_course(self):
        """حفظ بيانات الكورس"""
        # التحقق من صحة البيانات
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يجب إدخال اسم الكورس!")
            return
        
        if self.price_spin.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يجب إدخال سعر صحيح للكورس!")
            return
        
        try:
            # إنشاء أو تحديث كائن الكورس
            if self.course is None:
                self.course = Course()
            
            self.course.name = self.name_edit.text().strip()
            self.course.price = self.price_spin.value()
            self.course.sessions_count = self.sessions_spin.value()
            self.course.type = self.type_combo.currentText()
            self.course.session_link = self.session_link_edit.text().strip()
            self.course.description = self.description_edit.toPlainText().strip()
            
            # حفظ في قاعدة البيانات
            self.course.save()
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحفظ: {str(e)}")

class CoursesWidget(QWidget):
    """واجهة إدارة الكورسات"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_courses()
    
    def setup_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # شريط البحث والأزرار
        top_layout = QHBoxLayout()
        
        # البحث
        search_label = QLabel("البحث:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالاسم...")
        self.search_edit.textChanged.connect(self.search_courses)
        
        # فلتر النوع
        type_label = QLabel("النوع:")
        self.type_filter = QComboBox()
        self.type_filter.addItems(["الكل", "حضوري", "أونلاين"])
        self.type_filter.currentTextChanged.connect(self.filter_courses)
        
        # أزرار العمليات
        self.add_btn = QPushButton("إضافة كورس")
        self.edit_btn = QPushButton("تعديل")
        self.delete_btn = QPushButton("حذف")
        self.refresh_btn = QPushButton("تحديث")
        
        # ربط الأزرار بالوظائف
        self.add_btn.clicked.connect(self.add_course)
        self.edit_btn.clicked.connect(self.edit_course)
        self.delete_btn.clicked.connect(self.delete_course)
        self.refresh_btn.clicked.connect(self.load_courses)
        
        # تعطيل أزرار التعديل والحذف في البداية
        self.edit_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)
        
        top_layout.addWidget(search_label)
        top_layout.addWidget(self.search_edit)
        top_layout.addWidget(type_label)
        top_layout.addWidget(self.type_filter)
        top_layout.addStretch()
        top_layout.addWidget(self.add_btn)
        top_layout.addWidget(self.edit_btn)
        top_layout.addWidget(self.delete_btn)
        top_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(top_layout)
        
        # جدول الكورسات
        self.courses_table = QTableWidget()
        self.courses_table.setColumnCount(6)
        self.courses_table.setHorizontalHeaderLabels([
            "الرقم", "اسم الكورس", "السعر", "عدد الحصص", "النوع", "الوصف"
        ])
        
        # تخصيص الجدول
        header = self.courses_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        # تحديد عرض الأعمدة
        self.courses_table.setColumnWidth(0, 60)   # الرقم
        self.courses_table.setColumnWidth(1, 200)  # اسم الكورس
        self.courses_table.setColumnWidth(2, 100)  # السعر
        self.courses_table.setColumnWidth(3, 100)  # عدد الحصص
        self.courses_table.setColumnWidth(4, 80)   # النوع
        
        # ربط حدث تحديد الصف
        self.courses_table.selectionModel().selectionChanged.connect(self.on_selection_changed)
        self.courses_table.doubleClicked.connect(self.edit_course)
        
        layout.addWidget(self.courses_table)
        
        # تطبيق الستايل
        self.apply_style()
    
    def apply_style(self):
        """تطبيق الستايل على الواجهة"""
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #4CAF50;
                selection-color: white;
            }
            
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e0e0e0;
            }
            
            QHeaderView::section {
                background-color: #2E7D32;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
            
            QLineEdit, QComboBox {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 4px;
                font-size: 12px;
            }
            
            QLineEdit:focus, QComboBox:focus {
                border-color: #4CAF50;
            }
        """)
    
    def load_courses(self):
        """تحميل قائمة الكورسات"""
        try:
            courses = Course.get_all()
            self.populate_table(courses)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")
    
    def populate_table(self, courses):
        """ملء الجدول بالكورسات"""
        self.courses_table.setRowCount(len(courses))
        
        for row, course in enumerate(courses):
            self.courses_table.setItem(row, 0, QTableWidgetItem(str(course.id)))
            self.courses_table.setItem(row, 1, QTableWidgetItem(course.name))
            self.courses_table.setItem(row, 2, QTableWidgetItem(f"{course.price:.2f} ريال"))
            self.courses_table.setItem(row, 3, QTableWidgetItem(str(course.sessions_count)))
            self.courses_table.setItem(row, 4, QTableWidgetItem(course.type))
            self.courses_table.setItem(row, 5, QTableWidgetItem(course.description))
            
            # حفظ معرف الكورس في البيانات المخفية
            self.courses_table.item(row, 0).setData(Qt.UserRole, course.id)
    
    def search_courses(self):
        """البحث عن الكورسات"""
        self.filter_courses()
    
    def filter_courses(self):
        """فلترة الكورسات حسب النوع والبحث"""
        keyword = self.search_edit.text().strip()
        course_type = self.type_filter.currentText()
        
        try:
            all_courses = Course.get_all()
            filtered_courses = []
            
            for course in all_courses:
                # فلترة حسب النوع
                if course_type != "الكل" and course.type != course_type:
                    continue
                
                # فلترة حسب البحث
                if keyword and keyword.lower() not in course.name.lower():
                    continue
                
                filtered_courses.append(course)
            
            self.populate_table(filtered_courses)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث: {str(e)}")
    
    def on_selection_changed(self):
        """التعامل مع تغيير التحديد في الجدول"""
        has_selection = len(self.courses_table.selectionModel().selectedRows()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
    
    def add_course(self):
        """إضافة كورس جديد"""
        dialog = CourseDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_courses()
            QMessageBox.information(self, "نجح", "تم إضافة الكورس بنجاح!")
    
    def edit_course(self):
        """تعديل كورس محدد"""
        current_row = self.courses_table.currentRow()
        if current_row >= 0:
            course_id = self.courses_table.item(current_row, 0).data(Qt.UserRole)
            course = Course.get_by_id(course_id)
            
            if course:
                dialog = CourseDialog(course, parent=self)
                if dialog.exec_() == QDialog.Accepted:
                    self.load_courses()
                    QMessageBox.information(self, "نجح", "تم تحديث بيانات الكورس بنجاح!")
    
    def delete_course(self):
        """حذف كورس محدد"""
        current_row = self.courses_table.currentRow()
        if current_row >= 0:
            course_name = self.courses_table.item(current_row, 1).text()
            
            reply = QMessageBox.question(self, "تأكيد الحذف", 
                                       f"هل تريد حذف الكورس '{course_name}'؟\n"
                                       "هذا الإجراء لا يمكن التراجع عنه!",
                                       QMessageBox.Yes | QMessageBox.No,
                                       QMessageBox.No)
            
            if reply == QMessageBox.Yes:
                try:
                    course_id = self.courses_table.item(current_row, 0).data(Qt.UserRole)
                    course = Course.get_by_id(course_id)
                    
                    if course and course.delete():
                        self.load_courses()
                        QMessageBox.information(self, "نجح", "تم حذف الكورس بنجاح!")
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في حذف الكورس!")
                        
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")
